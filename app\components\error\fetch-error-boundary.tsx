"use client";

import React from 'react';
import { ErrorBoundary } from 'react-error-boundary';
import { AlertCircle, RefreshCw, Home } from 'lucide-react';
import { Button } from '@/app/components/ui/button';

interface FetchErrorFallbackProps {
  error: Error;
  resetErrorBoundary: () => void;
}

function FetchErrorFallback({ error, resetErrorBoundary }: FetchErrorFallbackProps) {
  const isFetchError = error.message.includes('fetch') || 
                      error.message.includes('Failed to fetch') ||
                      error.name === 'TypeError';

  const handleGoHome = () => {
    if (typeof window !== 'undefined') {
      window.location.href = '/';
    }
  };

  const handleRefresh = () => {
    if (typeof window !== 'undefined') {
      window.location.reload();
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900 px-4">
      <div className="max-w-md w-full">
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-8 text-center border border-gray-200 dark:border-gray-700">
          <div className="flex justify-center mb-6">
            <div className="p-3 bg-red-100 dark:bg-red-900/30 rounded-full">
              <AlertCircle className="h-8 w-8 text-red-600 dark:text-red-400" />
            </div>
          </div>
          
          <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-3">
            {isFetchError ? 'Koneksi Bermasalah' : 'Terjadi Kesalahan'}
          </h2>
          
          <p className="text-gray-600 dark:text-gray-400 mb-6 text-sm leading-relaxed">
            {isFetchError 
              ? 'Tidak dapat terhubung ke server. Periksa koneksi internet Anda dan coba lagi.'
              : 'Terjadi kesalahan yang tidak terduga. Silakan coba lagi atau kembali ke halaman utama.'
            }
          </p>

          {process.env.NODE_ENV === 'development' && (
            <details className="mb-6 text-left">
              <summary className="text-sm text-gray-500 dark:text-gray-400 cursor-pointer hover:text-gray-700 dark:hover:text-gray-300">
                Detail Error (Development)
              </summary>
              <pre className="mt-2 p-3 bg-gray-100 dark:bg-gray-700 rounded text-xs text-gray-800 dark:text-gray-200 overflow-auto">
                {error.message}
              </pre>
            </details>
          )}
          
          <div className="flex flex-col sm:flex-row gap-3">
            <Button
              onClick={resetErrorBoundary}
              className="flex-1 bg-violet-600 hover:bg-violet-700 text-white"
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              Coba Lagi
            </Button>
            
            <Button
              onClick={handleGoHome}
              variant="outline"
              className="flex-1"
            >
              <Home className="h-4 w-4 mr-2" />
              Ke Beranda
            </Button>
          </div>

          {isFetchError && (
            <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
              <Button
                onClick={handleRefresh}
                variant="ghost"
                size="sm"
                className="text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300"
              >
                Atau refresh halaman
              </Button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

interface FetchErrorBoundaryProps {
  children: React.ReactNode;
  fallback?: React.ComponentType<FetchErrorFallbackProps>;
}

export function FetchErrorBoundary({ children, fallback }: FetchErrorBoundaryProps) {
  return (
    <ErrorBoundary
      FallbackComponent={fallback || FetchErrorFallback}
      onError={(error, errorInfo) => {
        console.error('FetchErrorBoundary caught an error:', error, errorInfo);
        
        // Log to external service in production
        if (process.env.NODE_ENV === 'production') {
          // You can add error reporting service here
          // e.g., Sentry, LogRocket, etc.
        }
      }}
      onReset={() => {
        // Clear any cached data or reset state if needed
        if (typeof window !== 'undefined') {
          // Clear any problematic cache
          if ('caches' in window) {
            caches.keys().then(names => {
              names.forEach(name => {
                if (name.includes('next-auth') || name.includes('auth')) {
                  caches.delete(name);
                }
              });
            });
          }
        }
      }}
    >
      {children}
    </ErrorBoundary>
  );
}
