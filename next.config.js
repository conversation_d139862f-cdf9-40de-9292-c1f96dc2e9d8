/** @type {import('next').NextConfig} */
const nextConfig = {
  // Optimize webpack for better stability
  webpack: (config, { dev, isServer }) => {
    // Improve module resolution for NextAuth
    if (dev && !isServer) {
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
        net: false,
        tls: false,
      };
    }

    // Optimize chunk splitting
    config.optimization = {
      ...config.optimization,
      splitChunks: {
        ...config.optimization.splitChunks,
        cacheGroups: {
          ...config.optimization.splitChunks?.cacheGroups,
          vendor: {
            test: /[\\/]node_modules[\\/]/,
            name: "vendors",
            chunks: "all",
          },
        },
      },
    };

    return config;
  },

  // Reduce memory usage and improve stability
  experimental: {
    optimizePackageImports: ["next-auth", "@auth/core", "@auth/prisma-adapter"],
    turbo: {
      rules: {
        "*.svg": {
          loaders: ["@svgr/webpack"],
          as: "*.js",
        },
      },
    },
  },

  images: {
    remotePatterns: [
      {
        protocol: "https",
        hostname: "placehold.co",
      },
      {
        protocol: "https",
        hostname: "**",
      },
    ],
  },
  async headers() {
    return [
      {
        source: "/(.*)",
        headers: [
          {
            key: "Content-Security-Policy",
            value: [
              "default-src 'self'",
              "script-src 'self' 'unsafe-inline' 'unsafe-eval' blob: https://app.sandbox.midtrans.com https://api.sandbox.midtrans.com https://d2n7cdqdrlte95.cloudfront.net https://d2f3dnusg0rbp7.cloudfront.net https://pay.google.com https://js-agent.newrelic.com https://bam.nr-data.net",
              "worker-src 'self' blob:",
              "child-src 'self' blob:",
              "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com",
              "font-src 'self' https://fonts.gstatic.com",
              "img-src 'self' data: blob: https: http: https://*.openstreetmap.org https://*.tile.openstreetmap.org https://tile.openstreetmap.org",
              "connect-src 'self' https://api.sandbox.midtrans.com https://app.sandbox.midtrans.com https://nominatim.openstreetmap.org https://photon.komoot.io https://bam.nr-data.net https://*.openstreetmap.org https://*.tile.openstreetmap.org https://tile.openstreetmap.org",
              "frame-src 'self' https://app.sandbox.midtrans.com https://pay.google.com",
              "object-src 'none'",
              "base-uri 'self'",
              "form-action 'self'",
              "frame-ancestors 'none'",
              "upgrade-insecure-requests",
            ].join("; "),
          },
          {
            key: "X-Frame-Options",
            value: "SAMEORIGIN",
          },
          {
            key: "X-Content-Type-Options",
            value: "nosniff",
          },
          {
            key: "Referrer-Policy",
            value: "strict-origin-when-cross-origin",
          },
        ],
      },
    ];
  },
};

module.exports = nextConfig;
