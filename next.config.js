/** @type {import('next').NextConfig} */
const nextConfig = {
  // Optimize webpack for better stability
  webpack: (config, { dev, isServer }) => {
    // Fix module format compatibility issues
    config.resolve.extensionAlias = {
      ".js": [".js", ".ts", ".tsx"],
      ".jsx": [".jsx", ".tsx"],
    };

    // Fix specific NextAuth import issues
    config.resolve.alias = {
      ...config.resolve.alias,
      "next/server": require.resolve("next/server"),
      "next/headers": require.resolve("next/headers"),
      "next/navigation": require.resolve("next/navigation"),
    };

    // Improve module resolution for NextAuth and other packages
    if (dev && !isServer) {
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
        net: false,
        tls: false,
        crypto: false,
        stream: false,
        url: false,
        zlib: false,
        http: false,
        https: false,
        assert: false,
        os: false,
        path: false,
      };
    }

    // Fix exports/require compatibility
    config.module.rules.push({
      test: /\.m?js$/,
      resolve: {
        fullySpecified: false,
      },
    });

    // Add specific rule for NextAuth modules
    config.module.rules.push({
      test: /node_modules\/(next-auth|@auth)/,
      resolve: {
        fullySpecified: false,
      },
    });

    // Handle CommonJS/ESM compatibility
    config.experiments = {
      ...config.experiments,
      topLevelAwait: true,
    };

    // Optimize chunk splitting with better vendor separation
    if (!isServer) {
      config.optimization = {
        ...config.optimization,
        splitChunks: {
          ...config.optimization.splitChunks,
          cacheGroups: {
            ...config.optimization.splitChunks?.cacheGroups,
            vendor: {
              test: /[\\/]node_modules[\\/]/,
              name: "vendors",
              chunks: "all",
              enforce: true,
            },
            nextauth: {
              test: /[\\/]node_modules[\\/](next-auth|@auth)[\\/]/,
              name: "nextauth",
              chunks: "all",
              priority: 10,
            },
          },
        },
      };
    }

    return config;
  },

  // Reduce memory usage and improve stability
  experimental: {
    optimizePackageImports: ["lucide-react", "react-icons"],
    esmExternals: true,
    turbo: {
      rules: {
        "*.svg": {
          loaders: ["@svgr/webpack"],
          as: "*.js",
        },
      },
    },
  },

  // External packages for server components (moved from experimental)
  serverExternalPackages: ["next-auth", "@auth/core", "@auth/prisma-adapter"],

  images: {
    remotePatterns: [
      {
        protocol: "https",
        hostname: "placehold.co",
      },
      {
        protocol: "https",
        hostname: "**",
      },
    ],
  },
  async headers() {
    return [
      {
        source: "/(.*)",
        headers: [
          {
            key: "Content-Security-Policy",
            value: [
              "default-src 'self'",
              "script-src 'self' 'unsafe-inline' 'unsafe-eval' blob: https://app.sandbox.midtrans.com https://api.sandbox.midtrans.com https://d2n7cdqdrlte95.cloudfront.net https://d2f3dnusg0rbp7.cloudfront.net https://pay.google.com https://js-agent.newrelic.com https://bam.nr-data.net",
              "worker-src 'self' blob:",
              "child-src 'self' blob:",
              "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com",
              "font-src 'self' https://fonts.gstatic.com",
              "img-src 'self' data: blob: https: http: https://*.openstreetmap.org https://*.tile.openstreetmap.org https://tile.openstreetmap.org",
              "connect-src 'self' https://api.sandbox.midtrans.com https://app.sandbox.midtrans.com https://nominatim.openstreetmap.org https://photon.komoot.io https://bam.nr-data.net https://*.openstreetmap.org https://*.tile.openstreetmap.org https://tile.openstreetmap.org",
              "frame-src 'self' https://app.sandbox.midtrans.com https://pay.google.com",
              "object-src 'none'",
              "base-uri 'self'",
              "form-action 'self'",
              "frame-ancestors 'none'",
              "upgrade-insecure-requests",
            ].join("; "),
          },
          {
            key: "X-Frame-Options",
            value: "SAMEORIGIN",
          },
          {
            key: "X-Content-Type-Options",
            value: "nosniff",
          },
          {
            key: "Referrer-Policy",
            value: "strict-origin-when-cross-origin",
          },
        ],
      },
    ];
  },
};

module.exports = nextConfig;
