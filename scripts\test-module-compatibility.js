#!/usr/bin/env node

console.log('Testing module compatibility...');

// Test basic Node.js modules
try {
  const fs = require('fs');
  const path = require('path');
  console.log('✅ Basic Node.js modules work');
} catch (error) {
  console.log('❌ Basic Node.js modules failed:', error.message);
}

// Test Next.js
try {
  const next = require('next');
  console.log('✅ Next.js module loads correctly');
} catch (error) {
  console.log('❌ Next.js module failed:', error.message);
}

// Test NextAuth
try {
  const nextAuth = require('next-auth');
  console.log('✅ NextAuth module loads correctly');
} catch (error) {
  console.log('❌ NextAuth module failed:', error.message);
}

// Test Auth Core
try {
  const authCore = require('@auth/core');
  console.log('✅ Auth Core module loads correctly');
} catch (error) {
  console.log('❌ Auth Core module failed:', error.message);
}

// Test Prisma Adapter
try {
  const prismaAdapter = require('@auth/prisma-adapter');
  console.log('✅ Prisma Adapter module loads correctly');
} catch (error) {
  console.log('❌ Prisma Adapter module failed:', error.message);
}

console.log('\nModule compatibility test completed.');
console.log('If any modules failed, the "exports is not defined" error might persist.');
console.log('All modules should load correctly for the application to work properly.');
