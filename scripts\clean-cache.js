#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🧹 Cleaning Next.js cache and build artifacts...');

const pathsToClean = [
  '.next',
  'node_modules/.cache',
  '.vercel',
  'dist',
  'build'
];

function deleteFolderRecursive(folderPath) {
  if (fs.existsSync(folderPath)) {
    try {
      fs.rmSync(folderPath, { recursive: true, force: true });
      console.log(`✅ Deleted: ${folderPath}`);
    } catch (error) {
      console.log(`❌ Failed to delete ${folderPath}: ${error.message}`);
    }
  } else {
    console.log(`⏭️  Skipped: ${folderPath} (doesn't exist)`);
  }
}

// Clean cache directories
pathsToClean.forEach(deleteFolderRecursive);

// Clear npm cache
try {
  console.log('🔄 Clearing npm cache...');
  execSync('npm cache clean --force', { stdio: 'inherit' });
  console.log('✅ NPM cache cleared');
} catch (error) {
  console.log(`❌ Failed to clear npm cache: ${error.message}`);
}

// Reinstall dependencies
try {
  console.log('📦 Reinstalling dependencies...');
  execSync('npm install', { stdio: 'inherit' });
  console.log('✅ Dependencies reinstalled');
} catch (error) {
  console.log(`❌ Failed to reinstall dependencies: ${error.message}`);
}

console.log('🎉 Cache cleaning completed!');
console.log('💡 You can now run "npm run dev" to start the development server.');
