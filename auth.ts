export const runtime = 'nodejs'

import NextAuth from "next-auth"
import { PrismaAdapter } from "@auth/prisma-adapter"
import { prisma } from "@/lib/config/prisma"
import <PERSON> from "next-auth/providers/google"
import Cred<PERSON><PERSON><PERSON>rovider from "next-auth/providers/credentials"
import { compare } from "bcrypt-ts"
import type { DefaultSession, NextAuthConfig } from "next-auth"
import type { Adapter } from "next-auth/adapters"
import { SignInSchema } from "@/lib/validations/user/schema"
import { Role } from "@/lib/types/auth"

// Extend session type
declare module "next-auth" {
  interface Session extends DefaultSession {
    user: {
      id: string
      role: string
      phone?: string | null
      createdAt?: Date
    } & DefaultSession["user"]
  }
}

const config = {
  adapter: PrismaAdapter(prisma) as Adapter,
  session: {
    strategy: "jwt",
    maxAge: 30 * 24 * 60 * 60, // 30 hari
    updateAge: 24 * 60 * 60, // Update session every 24 hours
  },

  // Add debug configuration for development
  debug: process.env.NODE_ENV === 'development',

  // Improve error handling
  logger: {
    error(code, metadata) {
      console.error('NextAuth Error:', code, metadata);
    },
    warn(code) {
      console.warn('NextAuth Warning:', code);
    },
    debug(code, metadata) {
      if (process.env.NODE_ENV === 'development') {
        console.debug('NextAuth Debug:', code, metadata);
      }
    },
  },
  cookies: {
    sessionToken: {
      name: `next-auth.session-token`,
      options: {
        httpOnly: true,
        sameSite: 'lax',
        path: '/',
        secure: process.env.NODE_ENV === 'production',
        domain: process.env.NODE_ENV === 'production' ? process.env.NEXTAUTH_URL?.replace(/https?:\/\//, '') : undefined
      }
    },
    callbackUrl: {
      name: `next-auth.callback-url`,
      options: {
        sameSite: 'lax',
        path: '/',
        secure: process.env.NODE_ENV === 'production',
      }
    },
    csrfToken: {
      name: `next-auth.csrf-token`,
      options: {
        httpOnly: true,
        sameSite: 'lax',
        path: '/',
        secure: process.env.NODE_ENV === 'production',
      }
    }
  },
  pages: {
    signIn: "/login",
    error: "/auth/error",
    signOut: "/",
  },
  debug: process.env.NODE_ENV === 'development',
  trustHost: true,
  providers: [
    Google({
      clientId: process.env.GOOGLE_CLIENT_ID!,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET!,
    }),
    CredentialsProvider({
      name: "credentials",
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" }
      },
      async authorize(credentials) {
        try {
          if (!credentials?.email || !credentials?.password) {
            console.log("Missing credentials");
            return null;
          }

          const validatedFields = SignInSchema.safeParse(credentials);
          if (!validatedFields.success) {
            console.log("Validation failed:", validatedFields.error);
            return null;
          }

          const { email, password } = validatedFields.data;
          const user = await prisma.user.findFirst({
            where: { email: email.toLowerCase().trim() }
          });

          if (!user) {
            console.log("User not found:", email);
            return null;
          }

          if (!user.password) {
            console.log("User has no password:", email);
            return null;
          }

          const isPasswordValid = await compare(password, user.password);
          if (!isPasswordValid) {
            console.log("Invalid password for user:", email);
            return null;
          }

          console.log("User authenticated successfully:", email);
          return {
            id: user.id,
            name: user.name ?? null,
            email: user.email ?? null,
            role: (user.role ?? "USER") as Role,
            phone: user.phone ?? null,
            createdAt: user.createdAt ?? new Date(),
            emailVerified: user.emailVerified ?? null
          } as const;
        } catch (error) {
          console.error("Auth error:", error);
          return null;
        }
      }
    })
  ],
  callbacks: {
    async jwt({ token, user, trigger, session }) {
      if (trigger === "update" && session?.user) {
        token.name = session.user.name ?? token.name;
        token.email = session.user.email ?? token.email;
        token.picture = session.user.image ?? token.picture;
      }
      if (user) {
        token.id = user.id!;
        token.role = (user.role ?? "USER") as Role;
        token.phone = user.phone ?? null;
        token.createdAt = user.createdAt ?? new Date();
      }
      return token;
    },
    async session({ session, token }) {
      if (session.user) {
        session.user.id = token.id as string;
        session.user.role = token.role as Role;
        session.user.phone = token.phone as string | null;
        session.user.createdAt = token.createdAt as Date;
      }
      return session;
    }
  }
} satisfies NextAuthConfig

export const { auth, signIn, signOut, handlers: { GET, POST } } = NextAuth(config)
