#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

console.log('Patching NextAuth module compatibility issues...');

// Path to the problematic NextAuth file
const nextAuthEnvPath = path.join(__dirname, '..', 'node_modules', 'next-auth', 'lib', 'env.js');

if (fs.existsSync(nextAuthEnvPath)) {
  try {
    let content = fs.readFileSync(nextAuthEnvPath, 'utf8');
    
    // Replace the problematic import
    const originalImport = `from "next/server"`;
    const fixedImport = `from "next/server.js"`;
    
    if (content.includes(originalImport) && !content.includes(fixedImport)) {
      content = content.replace(originalImport, fixedImport);
      fs.writeFileSync(nextAuthEnvPath, content, 'utf8');
      console.log('✅ Patched NextAuth env.js import issue');
    } else {
      console.log('⏭️  NextAuth env.js already patched or no changes needed');
    }
  } catch (error) {
    console.log('❌ Failed to patch NextAuth env.js:', error.message);
  }
} else {
  console.log('⏭️  NextAuth env.js not found, skipping patch');
}

// Also check for other potential import issues
const nextAuthFiles = [
  'node_modules/next-auth/lib/index.js',
  'node_modules/next-auth/lib/utils.js',
  'node_modules/next-auth/lib/actions.js'
];

nextAuthFiles.forEach(filePath => {
  const fullPath = path.join(__dirname, '..', filePath);
  if (fs.existsSync(fullPath)) {
    try {
      let content = fs.readFileSync(fullPath, 'utf8');
      let modified = false;
      
      // Fix common import issues
      const fixes = [
        { from: `from "next/server"`, to: `from "next/server.js"` },
        { from: `from "next/headers"`, to: `from "next/headers.js"` },
        { from: `from "next/navigation"`, to: `from "next/navigation.js"` }
      ];
      
      fixes.forEach(fix => {
        if (content.includes(fix.from) && !content.includes(fix.to)) {
          content = content.replace(new RegExp(fix.from, 'g'), fix.to);
          modified = true;
        }
      });
      
      if (modified) {
        fs.writeFileSync(fullPath, content, 'utf8');
        console.log(`✅ Patched ${filePath}`);
      }
    } catch (error) {
      console.log(`❌ Failed to patch ${filePath}:`, error.message);
    }
  }
});

console.log('NextAuth patching completed.');
console.log('Note: This is a temporary fix. Consider upgrading to a stable version when available.');
