import type { <PERSON>ada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import { SessionProvider } from "@/app/components/providers/session-provider";
import { ThemeProvider } from "@/app/components/providers/theme-provider";
import ToasterProvider from "@/app/components/providers/toaster-provider";
import { auth } from "@/auth";
import { MidtransProvider } from "@/app/components/providers/midtrans-provider";
import { FetchErrorBoundary } from "@/app/components/error/fetch-error-boundary";

const inter = Inter({
  subsets: ["latin"],
  display: "swap",
  variable: "--font-inter",
});

export const metadata: Metadata = {
  title: "RentalGenset | Solusi Penyewaan Genset Terpercaya",
  description: "Platform rental genset modern dengan layanan profesional untuk berbagai kebutuhan listrik industri dan komersial",
  keywords: ["rental genset", "sewa genset", "generator listrik", "penyewaan genset"],
  authors: [{ name: "RentalGenset Team" }],
  creator: "RentalGenset",
  publisher: "RentalGenset",
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
};

export const viewport = {
  width: "device-width",
  initialScale: 1,
  maximumScale: 5,
  userScalable: true,
  themeColor: [
    { media: "(prefers-color-scheme: light)", color: "#ffffff" },
    { media: "(prefers-color-scheme: dark)", color: "#0f172a" },
  ],
};

export default async function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  // Menggunakan try-catch untuk mencegah error dari auth() mematikan aplikasi
  let session = null;
  try {
    session = await auth();
  } catch (error) {
    console.error("Error in auth() call:", error);
    // Tidak perlu throw error, biarkan aplikasi tetap berjalan
  }

  // Script untuk menangani tema
  const themeScript = `
    (function() {
      // Coba mengambil tema dari localStorage
      function getTheme() {
        try {
          return localStorage.getItem('rental-genset-theme') || 'light';
        } catch (e) {
          return 'light';
        }
      }

      // Fungsi untuk menerapkan tema
      function applyTheme(theme) {
        const d = document.documentElement;

        // Hapus kelas tema lama
        d.classList.remove('light', 'dark');

        // Tambahkan kelas untuk tema baru
        d.classList.add(theme);

        // Atur atribut data-theme
        d.setAttribute('data-theme', theme);
      }

      // Terapkan tema yang tersimpan
      var savedTheme = getTheme();
      applyTheme(savedTheme);

      // Tambahkan listener untuk perubahan tema
      document.addEventListener('themeChange', function(e) {
        if (e.detail && e.detail.theme) {
          applyTheme(e.detail.theme);
        }
      });
    })();
  `;

  // Script untuk menangani error fetch dari Next-Auth
  const errorHandlingScript = `
    (function() {
      // Error handling untuk fetch
      window.addEventListener('unhandledrejection', function(event) {
        if (event.reason && event.reason.toString().includes('Failed to fetch')) {
          console.warn('Unhandled fetch error detected:', event.reason);

          // Mencegah error mempengaruhi UI
          event.preventDefault();
          event.stopPropagation();
        }
      });
    })();
  `;

  return (
    <html lang="id" suppressHydrationWarning className={inter.variable}>
      <head>
        {/* Preload critical resources */}
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />

        {/* Script untuk menerapkan tema sebelum hydration untuk menghindari flicker */}
        <script dangerouslySetInnerHTML={{ __html: themeScript }} />
        {/* Script untuk menangani error fetch */}
        <script dangerouslySetInnerHTML={{ __html: errorHandlingScript }} />

        {/* Performance optimizations */}
        <meta name="format-detection" content="telephone=no, date=no, email=no, address=no" />
        <meta name="mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="default" />
      </head>
      <body
        className={`${inter.className} font-sans antialiased`}
        suppressHydrationWarning
      >
        <SessionProvider session={session}>
          <ThemeProvider
            attribute="data-theme"
            defaultTheme="light"
            enableSystem
            storageKey="rental-genset-theme"
          >
            {/* Modern responsive container with improved background */}
            <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-100 dark:from-gray-950 dark:via-gray-900 dark:to-gray-800 transition-colors duration-300">
              {/* Main content wrapper with better responsive behavior */}
              <div className="relative min-h-screen">
                <FetchErrorBoundary>
                  {children}
                </FetchErrorBoundary>
              </div>
            </div>

            {/* Global providers */}
            <ToasterProvider />
            <MidtransProvider />
          </ThemeProvider>
        </SessionProvider>
      </body>
    </html>
  );
}
