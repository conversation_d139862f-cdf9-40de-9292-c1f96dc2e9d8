'use client';

import { SessionProvider as NextAuthSessionProvider } from "next-auth/react";
import type { Session } from "next-auth";
import { useEffect, useState } from "react";

export function SessionProvider({ children, session }: {
    children: React.ReactNode;
    session: Session | null;
}) {
    const [hasError, setHasError] = useState(false);

    // Error handling untuk NextAuth fetch errors
    useEffect(() => {
        const handleError = (event: ErrorEvent) => {
            // Check jika error terkait dengan NextAuth fetch
            if (
                event.error?.toString().includes('Failed to fetch') &&
                (
                    event.error?.stack?.includes('next-auth') ||
                    event.message?.includes('next-auth')
                )
            ) {
                console.warn('NextAuth fetch error detected, disabling auto refetch');
                setHasError(true);
            }
        };

        const handleRejection = (event: PromiseRejectionEvent) => {
            if (
                event.reason?.toString().includes('Failed to fetch') &&
                (
                    event.reason?.stack?.includes('next-auth') ||
                    JSON.stringify(event.reason).includes('next-auth')
                )
            ) {
                console.warn('NextAuth unhandled rejection detected', event.reason);
                setHasError(true);
            }
        };

        window.addEventListener('error', handleError);
        window.addEventListener('unhandledrejection', handleRejection);

        return () => {
            window.removeEventListener('error', handleError);
            window.removeEventListener('unhandledrejection', handleRejection);
        };
    }, []);

    return (
        <NextAuthSessionProvider
            session={session}
            refetchInterval={hasError ? 0 : 60 * 5} // 5 menit jika tidak ada error, 0 jika ada error
            refetchOnWindowFocus={!hasError}
        >
            {children}
        </NextAuthSessionProvider>
    );
} 
